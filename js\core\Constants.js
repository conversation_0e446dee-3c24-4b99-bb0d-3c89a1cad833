// ===== GAME CONSTANTS =====
// Central configuration and constant values

const CONSTANTS = {
    // Dungeon Configuration
    DUNGEON_SIZE: 20,
    MAX_FLOORS: 10,
    
    // Player Starting Position
    PLAYER_START_X: 10,
    PLAYER_START_Y: 18,
    
    // Directions
    DIRECTIONS: {
        NORTH: 0,
        EAST: 1,
        SOUTH: 2,
        WEST: 3
    },
    
    // Direction Vectors
    DIRECTION_VECTORS: [
        { x: 0, y: -1 }, // North
        { x: 1, y: 0 },  // East
        { x: 0, y: 1 },  // South
        { x: -1, y: 0 }  // West
    ],
    
    // Combat Settings
    COMBAT: {
        EVASION_BASE: 0.02,
        EVASION_FACTOR: 0.003,
        MAX_EVASION: 0.18,
        MAGIC_EVASION_MODIFIER: 0.6,
        RANDOM_VARIANCE_PERCENT: 25,
        MIN_RANDOM_VALUE: 5
    },
    
    // Enemy Settings
    ENEMY: {
        BASE_COUNT: 8,
        COUNT_PER_FLOOR: 2,
        RESPAWN_TIMER: 45,
        RESPAWN_CHANCE: 0.2,
        RESPAWN_STAT_MULTIPLIER: 0.2,  // 20% stat increase per respawn
        RESPAWN_COUNT_REDUCTION: 1     // Reduce enemy count by 1 per respawn
    },
    
    // Post-Combat Healing
    POST_COMBAT: {
        HEALING_RATE_MIN: 0.10,  // 10% of max life
        HEALING_RATE_MAX: 0.15,  // 15% of max life
        ENHANCED_HEALING_RATE: 0.35  // 35% enhanced healing when wild meat obtained
    },
    
    // Multi-Enemy System
    MULTI_ENEMY: {
        SPAWN_CHANCE: 0.15,  // 15% chance for double enemy spawn
        STAT_VARIANCE_MIN: 0.8,  // Second enemy minimum stat multiplier
        STAT_VARIANCE_MAX: 1.2   // Second enemy maximum stat multiplier
    },
    
    // Trap Pit System
    TRAP_PIT: {
        BASE_SPAWN_CHANCE: 0.03,    // Base spawn chance: 3% on floor 1
        FLOOR_INCREASE_RATE: 0.02,  // Increase rate: 2% per floor
        DAMAGE_MIN: 0.10,           // Minimum damage: 10% of max life
        DAMAGE_MAX: 0.20            // Maximum damage: 20% of max life
    },
    
    // Mapping Tool System
    MAPPING_TOOL: {
        DROP_CHANCE: 0.03        // 3% chance for mapping tool to drop
    },

    // Beast Monster System
    BEAST_MONSTERS: {
        WILD_MEAT_DROP_CHANCE: 0.40  // 40% chance for wild meat drop from beast monsters
    },
    
    // Inventory System
    INVENTORY: {
        MAX_ITEM_STACK: 10,      // Maximum items per stack
        MAX_TOTAL_SLOTS: 20,     // Maximum total inventory slots
        COMBAT_DROP_CHANCE: 0.15 // 15% chance for item drop after combat
    },
    
    // Item Definitions
    ITEMS: {
        HEALTH_POTION: {
            id: 'health_potion',
            name: 'Health Potion',
            description: 'Restores 20% of max life',
            effect: 'heal',
            value: 0.2,              // 20% healing
            maxStack: 10,
            shopPrice: 500
        },
        SMOKE_SCREEN: {
            id: 'smoke_screen',
            name: 'Smoke Screen',
            description: 'Avoid enemy encounters for 10 steps',
            effect: 'stealth',
            value: 10,               // 10 steps duration
            maxStack: 10,
            shopPrice: 600
        },
        ANTIDOTE: {
            id: 'antidote',
            name: 'Antidote',
            description: 'Cures poison status effect',
            effect: 'cure_poison',
            value: 1,                // Removes poison
            maxStack: 10,
            shopPrice: 300
        },
        WILD_MEAT: {
            id: 'wild_meat',
            name: 'Wild Meat',
            description: 'Fresh meat from defeated beasts - enhances natural healing',
            effect: 'enhanced_healing',
            value: 0.35,             // 35% enhanced healing rate
            maxStack: 1,             // Not stored in inventory - auto-consumed
            shopPrice: 0             // Cannot be purchased
        },
        ORB: {
            id: 'orb',
            name: 'Orb',
            description: 'A mysterious orb with unknown power',
            effect: 'no_effect',     // No direct effect - passive collectible
            value: 0,                // No value
            maxStack: 10             // No shopPrice - drop-only item
        },
        MYSTIC_ORB: {
            id: 'mystic_orb',
            name: 'Mystic Orb',
            description: 'A mystical orb emanating otherworldly energy',
            effect: 'no_effect',     // No direct effect - passive collectible
            value: 0,                // No value
            maxStack: 10             // No shopPrice - drop-only item
        },
        MAPPING_TOOL: {
            id: 'mapping_tool',
            name: 'Mapping Tool',
            description: 'Reveals the dungeon map and enables minimap functionality',
            effect: 'mapping',
            value: 1,                // Enables mapping
            maxStack: 1,             // Only need one
            shopPrice: 1500          // Premium item
        }
    },
    
    // Monster Types
    MONSTER_TYPES: {
        IMP: {
            name: 'Imp',
            imagePath: 'assets/images/imp.png',
            properties: ['devil'],
            baseStats: {
                life: 50,
                str: 12,
                int: 15,
                agl: 20
            },
            behaviorPattern: {
                attackWeight: 0.5,    // 50% physical attacks
                magicWeight: 0.5,     // 50% magic attacks
                defendWeight: 0.0     // 0% defend
            },
            specialAbilities: {
                fleeChance: 0.20,         // 20% chance to flee from combat each turn
                allySummonChance: 0.20    // 20% chance to summon ally when in combat
            },
            minFloor: 1,
            maxFloor: 2
        },
        SLIME: {
            name: 'Slime',
            imagePath: 'assets/images/slime.png',
            properties: [],
            baseStats: {
                life: 60,
                str: 14,
                int: 12,
                agl: 10
            },
            behaviorPattern: {
                attackWeight: 0.70,        // 70% physical attacks
                magicWeight: 0.10,         // 10% magic attacks
                defendWeight: 0.10,        // 10% defend
                poisonAttackWeight: 0.10   // 10% poison attacks
            },
            minFloor: 1,                   // Appears from floor 1 onwards
            maxFloor: 2                    // Appears up to floor 2
        },
        GHOST: {
            name: 'Ghost',
            imagePath: 'assets/images/ghost.png',
            properties: ['spiritual_body'],
            baseStats: {
                life: 65,
                str: 12,
                int: 20,
                agl: 22
            },
            behaviorPattern: {
                attackWeight: 0.1,    // 10% physical attacks
                magicWeight: 0.8,     // 80% magic attacks
                defendWeight: 0.1     // 10% defend
            },
            minFloor: 1,
            maxFloor: 3
        },
        GOBLIN: {
            name: 'Goblin',
            imagePath: 'assets/images/goblin.png',
            properties: [],
            baseStats: {
                life: 60,
                str: 16,
                int: 8,
                agl: 18
            },
            behaviorPattern: {
                attackWeight: 0.4,    // 40% physical attacks
                magicWeight: 0.3,     // 30% magic attacks
                defendWeight: 0.2,    // 20% defend
                poisonAttackWeight: 0.1  // 10% poison attacks
            },
            minFloor: 1,
            maxFloor: 3
        },
        HELL_HOUND: {
            name: 'Hell Hound',
            imagePath: 'assets/images/hell hound.png',
            properties: ['beast'],
            baseStats: {
                life: 60,
                str: 18,
                int: 6,
                agl: 22
            },
            behaviorPattern: {
                attackWeight: 0.80,       // 80% physical attacks
                magicWeight: 0.0,        // 0% magic attacks
                defendWeight: 0.20,       // 20% defend
                doubleActionChance: 0.25 // 25% chance for double action
            },
            minFloor: 1,
            maxFloor: 3
        },
        ORC: {
            name: 'Orc',
            imagePath: 'assets/images/orc.png',
            properties: ['giant'],
            baseStats: {
                life: 80,
                str: 22,
                int: 4,
                agl: 16
            },
            behaviorPattern: {
                attackWeight: 0.90,    // 80% physical attacks
                magicWeight: 0.02,    // 2% magic attacks
                defendWeight: 0.08    // 8% defend
            },
            minFloor: 2,
            maxFloor: 4
        },
        ZOMBIE: {
            name: 'Zombie',
            imagePath: 'assets/images/Zombie.png',
            properties: ['spiritual_body'],
            baseStats: {
                life: 90,    // High life
                str: 18,
                int: 2,
                agl: 8       // Low agility
            },
            behaviorPattern: {
                attackWeight: 0.70,        // 70% physical attacks
                magicWeight: 0.05,         // 5% magic attacks
                defendWeight: 0.10,        // 10% defend
                poisonAttackWeight: 0.15   // 15% poison attacks
            },
            specialAbilities: {
                reviveChance: 0.30         // 30% chance to revive when life reaches 0
            },
            minFloor: 3,                   // Only appears from floor 3 onwards
            maxFloor: 5
        },
        TROLL: {
            name: 'Troll',
            imagePath: 'assets/images/troll.png',
            properties: ['giant'],
            baseStats: {
                life: 85,
                str: 20,
                int: 12,
                agl: 18
            },
            behaviorPattern: {
                attackWeight: 0.85,        // 85% physical attacks
                magicWeight: 0.05,         // 5% magic attacks
                defendWeight: 0.10         // 10% defend
            },
            specialAbilities: {
                criticalHitChance: 0.03,   // 3% critical hit chance
                stunChance: 0.05           // 5% chance to stun on physical attack hits
            },
            minFloor: 3,                   // Appears from floor 3 onwards
            maxFloor: 4                    // Appears up to floor 4
        },
        WING_LIZARD: {
            name: 'Wing Lizard',
            imagePath: 'assets/images/wing rizard.png',
            properties: [],
            baseStats: {
                life: 120,   // High life
                str: 22,
                int: 12,
                agl: 23      // High agility
            },
            behaviorPattern: {
                attackWeight: 0.60,        // 60% physical attacks
                magicWeight: 0.20,         // 20% magic attacks
                defendWeight: 0.10,        // 10% defend
                breathAttackWeight: 0.10   // 10% breath attacks
            },
            specialAbilities: {
                criticalHitChance: 0.15,   // 15% chance for 1.5x damage on normal attacks
                breathAttackChance: 0.10,  // 10% chance for breath attack (area damage)
                rangerWeakness: 1.2        // Takes 1.2x damage from Ranger attacks
            },
            minFloor: 5,                   // Only appears from floor 5 onwards
            maxFloor: 6                    // Only appears up to floor 7
        },
        FIEND: {
            name: 'Fiend',
            imagePath: 'assets/images/fiend.png',
            properties: ['spiritual_body'],
            baseStats: {
                life: 85,
                str: 16,
                int: 20,
                agl: 21
            },
            behaviorPattern: {
                attackWeight: 0.0,         // 0% physical attacks - never uses physical attacks
                magicWeight: 0.85,         // 85% magic attacks
                defendWeight: 0.15,        // 15% defend
                screamAttackWeight: 0.15   // 15% scream attacks (overlaps with magic)
            },
            specialAbilities: {
                screamChance: 0.15         // 15% chance to trigger scream on any attack
            },
            minFloor: 4,                   // Appears from floor 4 onwards
            maxFloor: 6                    // Appears up to floor 8
        },
        WILL_O_THE_WISP: {
            name: 'Will-o\'-the-wisp',
            imagePath: 'assets/images/willowisp.png',
            properties: ['nonmaterial'],
            baseStats: {
                life: 70,
                str: 0,
                int: 18,
                agl: 23
            },
            behaviorPattern: {
                attackWeight: 0.0,         // 0% physical attacks - magic only
                magicWeight: 0.85,         // 85% magic attacks
                defendWeight: 0.15         // 15% defend
            },
            specialAbilities: {
                physicalImmunity: true,    // Immune to physical damage
                allySummonChance: 0.20     // 20% chance to summon ally when alone
            },
            minFloor: 4,                   // Appears from floor 4 onwards
            maxFloor: 6                    // Appears up to floor 6
        },
        GHOUL: {
            name: 'Ghoul',
            imagePath: 'assets/images/ghoul.png',
            properties: ['undead'],
            baseStats: {
                life: 90,
                str: 18,
                int: 10,
                agl: 20
            },
            behaviorPattern: {
                attackWeight: 0.70,        // 70% physical attacks
                magicWeight: 0.0,          // 0% magic attacks - physical only
                defendWeight: 0.15,        // 15% defend
                poisonAttackWeight: 0.15   // 15% poison attacks
            },
            specialAbilities: {
                lifeDrain: 0.50            // Recovers 50% of damage dealt as life
            },
            minFloor: 3,                   // Appears from floor 3 onwards
            maxFloor: 5                    // Appears up to floor 6
        },
        GOLEM: {
            name: 'Golem',
            imagePath: 'assets/images/golem.png',
            properties: ['magical_creature'],
            baseStats: {
                life: 160,
                str: 26,
                int: 0,
                agl: 10
            },
            behaviorPattern: {
                attackWeight: 1.0,         // 100% physical attacks - cannot use magic
                magicWeight: 0.0,          // 0% magic attacks - no magical abilities
                defendWeight: 0.0          // 0% defend - always attacks
            },
            specialAbilities: {
                criticalHitChance: 0.15,   // 15% chance for 1.5x damage
                physicalDamageReduction: 0.5,  // Takes 50% reduced damage from physical attacks
                missChance: 0.25           // 25% miss chance on attacks against players
            },
            minFloor: 6,                   // Appears from floor 6 onwards
            maxFloor: 8                    // Appears up to floor 8
        },
        VENOMOUS_SNAKE: {
            name: 'Venomous Snake',
            imagePath: 'assets/images/snake.png',
            properties: ['beast'],
            baseStats: {
                life: 60,
                str: 16,
                int: 6,
                agl: 18
            },
            behaviorPattern: {
                attackWeight: 0.75,        // 75% physical attacks
                magicWeight: 0.0,          // 0% magic attacks - no magic
                defendWeight: 0.05,        // 5% defend
                poisonAttackWeight: 0.20   // 20% poison attacks
            },
            specialAbilities: {
                criticalHitChance: 0.05    // 5% critical hit chance
            },
            minFloor: 2,                   // Appears from floor 2 onwards
            maxFloor: 4                    // Appears up to floor 4
        },
        SKELETON_KNIGHT: {
            name: 'Skeleton Knight',
            imagePath: 'assets/images/skelton knight.png',
            properties: ['spiritual_body'],
            baseStats: {
                life: 130,
                str: 20,
                int: 16,
                agl: 18
            },
            behaviorPattern: {
                attackWeight: 0.75,        // 75% physical attacks
                magicWeight: 0.15,         // 15% magic attacks
                defendWeight: 0.10         // 10% defend
            },
            specialAbilities: {
                criticalHitChance: 0.10,           // 10% chance for critical hits
                doubleActionChance: 0.15,          // 15% chance for double action per turn
                physicalDamageReductionChance: 0.20 // 20% chance to reduce incoming physical damage by 50%
            },
            minFloor: 6,                   // Appears from floor 5 onwards
            maxFloor: 7                    // Appears up to floor 7
        },
        DRAGON: {
            name: 'Dragon',
            imagePath: 'assets/images/dragon.png',
            properties: ['dragon'],
            baseStats: {
                life: 150,
                str: 24,
                int: 20,
                agl: 18
            },
            behaviorPattern: {
                attackWeight: 0.70,        // 65% physical attacks
                magicWeight: 0.15,         // 20% magic attacks
                breathAttackWeight: 0.15,  // 15% breath attacks
                defendWeight: 0.0          // 0% defend
            },
            specialAbilities: {
                criticalHitChance: 0.05,           // 5% chance for critical hits
                physicalAttackNullifyChance: 0.20  // 20% chance to nullify incoming physical attacks (soaring)
            },
            minFloor: 8,                   // Appears from floor 8 onwards
            maxFloor: 10                   // Appears up to floor 10
        },
        RED_DRAGON: {
            name: 'Red Dragon',
            imagePath: 'assets/images/red dragon.png',
            properties: ['dragon'],
            baseStats: {
                life: 170,
                str: 25,
                int: 22,
                agl: 20
            },
            behaviorPattern: {
                attackWeight: 0.60,        // 60% physical attacks
                magicWeight: 0.15,         // 15% magic attacks
                breathAttackWeight: 0.25,  // 25% breath attacks
                defendWeight: 0.0          // 0% defend
            },
            specialAbilities: {
                criticalHitChance: 0.10    // 10% chance for critical hits
            },
            soloSpawn: true,               // Always spawns alone, never in groups
            minFloor: 9,                   // Appears from floor 9 onwards
            maxFloor: 10                   // Appears up to floor 10
        },
        PHANTOM: {
            name: 'Phantom',
            imagePath: 'assets/images/phantom.png',
            properties: ['spiritual_body'],
            baseStats: {
                life: 90,
                str: 15,
                int: 24,
                agl: 18
            },
            behaviorPattern: {
                attackWeight: 0.20,        // 20% physical attacks
                magicWeight: 0.60,         // 60% magic attacks
                defendWeight: 0.0,         // 0% defend
                screamAttackWeight: 0.20   // 20% scream attacks
            },
            specialAbilities: {
                lifeDrain: 0.35            // Recovers 35% of damage dealt as life
            },
            minFloor: 5,                   // Appears from floor 5 onwards
            maxFloor: 6                    // Appears up to floor 6
        },
        TIGER: {
            name: 'Tiger',
            imagePath: 'assets/images/tiger.png',
            properties: ['beast'],
            baseStats: {
                life: 80,
                str: 23,
                int: 10,
                agl: 18
            },
            behaviorPattern: {
                attackWeight: 0.80,        // 80% physical attacks
                magicWeight: 0.0,          // 0% magic attacks
                defendWeight: 0.20         // 20% defend
            },
            specialAbilities: {
                criticalHitChance: 0.07    // 7% critical hit chance
            },
            minFloor: 4,                   // Appears from floor 4 onwards
            maxFloor: 5                    // Appears up to floor 6
        },
        MIRE_STALKER: {
            name: 'Mire Stalker',
            imagePath: 'assets/images/MireStalker.png',
            properties: ['magical_creature'],
            baseStats: {
                life: 140,
                str: 22,
                int: 22,
                agl: 15
            },
            behaviorPattern: {
                attackWeight: 0.20,        // 20% physical attacks
                magicWeight: 0.25,         // 25% magic attacks
                defendWeight: 0.0,         // 0% defend
                breathAttackWeight: 0.15,  // 15% breath attacks
                poisonAttackWeight: 0.40   // 40% poison attacks
            },
            specialAbilities: {
                lifeDrain: 0.20,           // 20% chance to recover life from damage dealt
                criticalHitChance: 0.05    // 5% critical hit chance
            },
            minFloor: 7,                   // Appears from floor 7 onwards
            maxFloor: 8                    // Appears up to floor 8
        },
        DEMON: {
            name: 'Demon',
            imagePath: 'assets/images/demon.png',
            properties: ['otherworld'],
            baseStats: {
                life: 100,
                str: 19,
                int: 22,
                agl: 18
            },
            behaviorPattern: {
                attackWeight: 0.35,        // 35% physical attacks
                magicWeight: 0.55,         // 55% magic attacks
                defendWeight: 0.10         // 10% defend
            },
            specialAbilities: {
                lifeDrain: 0.40,           // 40% chance to recover life from damage dealt
                areaMagicAttackChance: 0.10 // 10% chance to perform area magic attack
            },
            minFloor: 6,                   // Appears from floor 6 onwards
            maxFloor: 9                    // Appears up to floor 9
        },
        GIANT_SPIDER: {
            name: 'Giant Spider',
            imagePath: 'assets/images/giant spider.png',
            properties: [],
            baseStats: {
                life: 100,
                str: 19,
                int: 17,
                agl: 18
            },
            behaviorPattern: {
                attackWeight: 0.50,        // 50% physical attacks
                magicWeight: 0.20,         // 20% magic attacks
                defendWeight: 0.05,        // 5% defend
                spiderWebAttackWeight: 0.25 // 25% spider web attacks
            },
            specialAbilities: {
                lifeDrain: 0.25            // 25% chance to recover life from damage dealt
            },
            minFloor: 6,                   // Appears from floor 6 onwards
            maxFloor: 7                    // Appears up to floor 7
        },
        LESSER_DEMON: {
            name: 'Lesser Demon',
            imagePath: 'assets/images/lesser demon.png',
            properties: ['devil'],
            baseStats: {
                life: 95,
                str: 19,
                int: 21,
                agl: 18
            },
            behaviorPattern: {
                attackWeight: 0.40,        // 40% physical attacks
                magicWeight: 0.50,         // 50% magic attacks
                defendWeight: 0.10         // 10% defend
            },
            specialAbilities: {
                lifeDrain: 0.25            // 25% chance to recover life from damage dealt
            },
            minFloor: 5,                   // Appears from floor 5 onwards
            maxFloor: 7                    // Appears up to floor 7
        },
        DEVIL: {
            name: 'Devil',
            imagePath: 'assets/images/devil.png',
            properties: ['devil'],
            baseStats: {
                life: 90,
                str: 16,
                int: 21,
                agl: 19
            },
            behaviorPattern: {
                attackWeight: 0.30,        // 30% physical attacks
                magicWeight: 0.60,         // 60% magic attacks
                defendWeight: 0.10         // 10% defend
            },
            specialAbilities: {
                criticalHitChance: 0.05,           // 5% critical hit chance
                areaMagicAttackChance: 0.05,       // 5% area magic attack chance
                lifeDrain: 0.20                    // 20% life drain ability
            },
            minFloor: 5,                   // Appears from floor 5 onwards
            maxFloor: 6                    // Appears up to floor 6
        },
        KILLER_WOLF: {
            name: 'Killer Wolf',
            imagePath: 'assets/images/killer wolf.png',
            properties: ['beastman'],
            baseStats: {
                life: 85,
                str: 20,
                int: 14,
                agl: 20
            },
            behaviorPattern: {
                attackWeight: 0.80,        // 80% physical attacks
                magicWeight: 0.10,         // 10% magic attacks
                defendWeight: 0.10,        // 10% defend
                doubleActionChance: 0.10   // 10% chance for double action
            },
            specialAbilities: {
                criticalHitChance: 0.05    // 5% chance for 1.5x damage
            },
            minFloor: 4,                   // Appears from floor 4 onwards
            maxFloor: 5                    // Appears up to floor 5
        },
        GIANT_ORC: {
            name: 'Giant Orc',
            imagePath: 'assets/images/giant orc.png',
            properties: ['giant'],
            baseStats: {
                life: 95,
                str: 24,
                int: 8,
                agl: 15
            },
            behaviorPattern: {
                attackWeight: 0.90,        // 90% physical attacks
                magicWeight: 0.02,         // 2% magic attacks
                defendWeight: 0.08         // 8% defend
            },
            specialAbilities: {
                criticalHitChance: 0.03    // 3% critical hit chance
            },
            minFloor: 4,                   // Appears from floor 4 onwards
            maxFloor: 6                    // Appears up to floor 6
        },
        DARK_ANGEL: {
            name: 'Dark Angel',
            imagePath: 'assets/images/dark angel.png',
            properties: ['otherworld'],
            baseStats: {
                life: 130,
                str: 16,
                int: 29,
                agl: 18
            },
            behaviorPattern: {
                attackWeight: 0.20,        // 20% physical attacks
                magicWeight: 0.70,         // 70% magic attacks
                defendWeight: 0.10         // 10% defend
            },
            specialAbilities: {
                criticalHitChance: 0.10,                // 10% critical hit chance
                areaMagicAttackChance: 0.15,            // 15% area magic attack chance
                lifeDrain: 0.40,                        // 40% life drain ability
                physicalAttackNullifyChance: 0.10       // 10% chance to nullify incoming physical attacks (soaring)
            },
            soloSpawn: true,               // Always spawns alone, never in groups
            minFloor: 9,                   // Appears from floor 9 onwards
            maxFloor: 10                   // Appears up to floor 10
        },
        GREATER_DEMON: {
            name: 'Greater Demon',
            imagePath: 'assets/images/greater demon.png',
            properties: ['otherworld'],
            baseStats: {
                life: 135,
                str: 20,
                int: 27,
                agl: 18
            },
            behaviorPattern: {
                attackWeight: 0.25,        // 25% physical attacks
                magicWeight: 0.65,         // 65% magic attacks
                defendWeight: 0.10         // 10% defend
            },
            specialAbilities: {
                criticalHitChance: 0.05,                    // 5% critical hit chance
                areaMagicAttackChance: 0.20,                // 20% area magic attack chance
                lifeDrain: 0.40,                            // 40% life drain ability (works on all attack types)
                stunChance: 0.10,                           // 10% stun chance on physical hits
                physicalAttackNullifyChance: 0.10           // 10% soaring ability - nullifies incoming physical attacks
            },
            soloSpawn: true,               // Always spawns alone, never in groups
            minFloor: 9,                   // Appears from floor 9 onwards
            maxFloor: 10                   // Appears up to floor 10
        },
        GIANT: {
            name: 'Giant',
            imagePath: 'assets/images/giant.png',
            properties: ['giant'],
            baseStats: {
                life: 155,
                str: 24,
                int: 18,
                agl: 17
            },
            behaviorPattern: {
                attackWeight: 0.80,        // 80% physical attacks
                magicWeight: 0.15,         // 15% magic attacks
                defendWeight: 0.05         // 5% defend
            },
            specialAbilities: {
                criticalHitChance: 0.10,   // 10% critical hit chance
                stunChance: 0.15           // 15% chance to stun on physical attack hits
            },
            minFloor: 7,                   // Appears from floor 7 onwards
            maxFloor: 9                    // Appears up to floor 9
        },
        GIANT_TROLL: {
            name: 'Giant Troll',
            imagePath: 'assets/images/giant troll.png',
            properties: ['giant'],
            baseStats: {
                life: 100,
                str: 22,
                int: 16,
                agl: 18
            },
            behaviorPattern: {
                attackWeight: 0.85,        // 85% physical attacks
                magicWeight: 0.15,         // 15% magic attacks
                defendWeight: 0.0          // 0% defend
            },
            specialAbilities: {
                criticalHitChance: 0.05,   // 5% critical hit chance
                stunChance: 0.10           // 10% chance to stun on physical attack hits
            },
            minFloor: 5,                   // Appears from floor 5 onwards
            maxFloor: 6                    // Appears up to floor 6
        },
        STALKER: {
            name: 'Stalker',
            imagePath: 'assets/images/stalker.png',
            properties: ['spiritual_body'],
            baseStats: {
                life: 90,
                str: 12,
                int: 22,
                agl: 18
            },
            behaviorPattern: {
                attackWeight: 0.05,        // 5% physical attacks
                magicWeight: 0.80,         // 75% magic attacks (adjusted to account for scream overlap)
                defendWeight: 0.0,         // 0% defend
                screamAttackWeight: 0.15   // 20% scream attacks (overlaps with magic)
            },
            specialAbilities: {
                screamChance: 0.20,        // 20% chance to trigger scream on any attack
                lifeDrain: 0.45            // 45% chance to recover life from damage dealt
            },
            minFloor: 6,                   // Appears from floor 6 onwards
            maxFloor: 7                    // Appears up to floor 7
        },
        DEMON_ELITE: {
            name: 'Demon Elite',
            imagePath: 'assets/images/demon elite.png',
            properties: ['otherworld'],
            baseStats: {
                life: 120,
                str: 21,
                int: 24,
                agl: 18
            },
            behaviorPattern: {
                attackWeight: 0.25,        // 25% physical attacks
                magicWeight: 0.65,         // 65% magic attacks
                defendWeight: 0.10         // 10% defend
            },
            specialAbilities: {
                areaMagicAttackChance: 0.15,  // 15% chance for area magic attacks
                lifeDrain: 0.40,              // 40% chance for life drain attacks
                criticalHitChance: 0.05,      // 5% chance for critical hits
                stunChance: 0.05              // 5% chance to stun player
            },
            minFloor: 8,                   // Appears from floor 8 onwards
            maxFloor: 9                    // Appears up to floor 9
        },
        VAMPIRE_LORD: {
            name: 'Vampire Lord',
            imagePath: 'assets/images/vampire lord.png',
            properties: ['undead'],
            baseStats: {
                life: 135,
                str: 20,
                int: 24,
                agl: 21
            },
            behaviorPattern: {
                attackWeight: 0.45,        // 45% physical attacks
                magicWeight: 0.45,         // 45% magic attacks
                screamAttackWeight: 0.10   // 10% scream attacks
            },
            specialAbilities: {
                areaMagicAttackChance: 0.10,        // 10% chance for area magic attacks
                criticalHitChance: 0.10,            // 10% chance for critical hits
                lifeDrain: 0.50,                    // 50% chance to drain life from enemies
                physicalAttackNullifyChance: 0.05   // 5% chance to nullify physical attacks
            },
            soloSpawn: true,               // Always spawns alone, never in groups
            minFloor: 9,                   // Appears from floor 9 onwards
            maxFloor: 10                   // Appears up to floor 10
        },
        ASSASSIN: {
            name: 'Assassin',
            imagePath: 'assets/images/assassin.png',
            properties: ['human'],
            baseStats: {
                life: 100,
                str: 21,
                int: 17,
                agl: 26
            },
            behaviorPattern: {
                attackWeight: 0.70,        // 70% physical attacks
                magicWeight: 0.15,         // 15% magic attacks
                poisonAttackWeight: 0.15   // 15% poison attacks
            },
            specialAbilities: {
                criticalHitChance: 0.25,   // 25% critical hit chance
                stunChance: 0.15           // 15% stun chance on physical hits
            },
            soloSpawn: true,               // Always spawns alone, never in groups
            minFloor: 8,                   // Appears from floor 8 onwards
            maxFloor: 9                    // Appears up to floor 9
        }
    },
    
    // Boss Types
    BOSS_TYPES: {
        DARK_LORD: {
            name: 'Dark Lord',
            imagePath: 'assets/images/dark load.png',
            baseStats: {
                life: 1000,
                str: 80,
                int: 100,
                agl: 80
            },
            isFinalBoss: true,
            specialAttackInterval: 3
        },
        ELITE_MONSTER: {
            name: 'Elite Monster',
            imagePath: 'assets/images/elite monster.png',
            baseStats: {
                life: 500,
                str: 90,
                int: 60,
                agl: 100
            },
            isFinalBoss: false
        }
    },
    
    // Character Classes
    CLASSES: {
        WARRIOR: {
            name: 'Warrior',
            life: 400,
            str: 255,
            int: 10,
            agl: 16
        },
        MAGE: {
            name: 'Mage',
            life: 500,
            str: 12,
            int: 252,
            agl: 12
        },
        RANGER: {
            name: 'Ranger',
            life: 500,
            str: 208,
            int: 15,
            agl: 255
        }
    },
    
    // Level Up Growth Rates
    GROWTH_RATES: {
        WARRIOR: {
            life: { min: 8, max: 13 },
            str: { min: 3, max: 6 },
            int: { min: 1, max: 2 },
            agl: { min: 2, max: 3 }
        },
        MAGE: {
            life: { min: 5, max: 9 },
            str: { min: 2, max: 4 },
            int: { min: 4, max: 8 },
            agl: { min: 2, max: 3 }
        },
        RANGER: {
            life: { min: 7, max: 10 },
            str: { min: 3, max: 5 },
            int: { min: 2, max: 4 },
            agl: { min: 3, max: 5 }
        }
    },
    
    // Shop System
    SHOPS: {
        MERCHANT: {
            type: 'merchant',
            name: 'Merchant',
            image: 'assets/images/Merchant.png',
            item: 'Life Potion',
            cost: 1000,
            effect: 'maxLife',
            value: 5
        },
        FIGHTER: {
            type: 'fighter',
            name: 'Combat Trainer',
            image: 'assets/images/Combat trainer.png',
            item: 'Training',
            cost: 1000,
            effect: 'str',
            value: 1
        },
        PRIEST: {
            type: 'priest',
            name: 'Chapel',
            image: 'assets/images/Priest.png',
            item: 'Prayer',
            cost: 500,
            effect: 'life',
            value: 'full'  // Special value indicating full heal
        },
        WITCH: {
            type: 'witch',
            name: 'Witch Shop',
            image: 'assets/images/Magical shop.png',
            item: 'Magic Potion',
            cost: 1000,
            effect: 'int',
            value: 1
        }
    },

    // Rest Service Pricing
    REST_COSTS: {
        CHAPEL: 100,    // Chapel offers cheaper rest services
        DEFAULT: 300    // All other shops charge more for rest
    },

    // Skill System
    SKILLS: {
        LEARNING_LEVELS: [5, 10, 15, 20, 25, 30],
        CONCENTRATE_DAMAGE_MULTIPLIER: 1.5,
        
        // Level 5 Skills Configuration
        LEVEL_5_SKILLS: {
            SHIELD: {
                id: 'shield',
                name: 'SHIELD',
                description: 'すべての敵の攻撃を自分が引き受ける。受けるダメージは1/3になる。2ターンの間有効',
                damageReduction: 1/3,  // ダメージを1/3に軽減
                duration: 2  // 2ターンの間有効
            },
            EXORCISE: {
                id: 'exorcise',
                name: 'EXORCISE',
                description: '霊体の敵に3倍のダメージを与える魔法攻撃。単体対象',
                spiritualDamageMultiplier: 3,  // 3x damage against spiritual enemies
                targetType: 'enemy'            // Single enemy target
            },
            FIRST_AID: {
                id: 'first_aid',
                name: 'FIRST AID',
                description: 'ターゲットとして選択した味方一人のLIFEを35％回復',
                healingRate: 0.35,      // 最大LIFEの35%回復
                targetType: 'ally'     // 味方をターゲットとする
            }
        },

        // Level 10 Skills Configuration
        LEVEL_10_SKILLS: {
            TRIPLE_STRIKE: {
                id: 'triple_strike',
                name: 'TRIPLE STRIKE',
                description: '単体の敵に3回連続攻撃を行う。各攻撃は通常攻撃と同じダメージ計算',
                attackCount: 3,        // 3回攻撃
                targetType: 'enemy'    // 敵をターゲットとする
            },
            SILENCE: {
                id: 'silence',
                name: 'SILENCE',
                description: '全ての敵を沈黙状態にし、5-8ターンの間魔法攻撃を封じる',
                duration: { min: 5, max: 8 },  // 5-8ターン持続
                targetType: 'all_enemies'       // 全敵をターゲット
            },
            HIDE: {
                id: 'hide',
                name: 'HIDE',
                description: '6ターンの間隠れ状態になり、敵の攻撃対象から除外される。攻撃は可能',
                duration: 6,           // 3ターン持続
                targetType: 'self'     // 自分をターゲット
            }
        },

        // Level 15 Skills Configuration
        LEVEL_15_SKILLS: {
            MELEE: {
                id: 'melee',
                name: 'MELEE',
                description: '自分の最大LIFEの15%を消費し、全ての敵に2倍の物理ダメージを与える範囲攻撃',
                lifeCostRate: 0.15,    // 最大LIFEの15%を消費
                damageMultiplier: 2.0, // 2倍ダメージ
                targetType: 'all_enemies'  // 全敵をターゲット
            },
            HEALING_LIGHT: {
                id: 'healing_light',
                name: 'HEALING LIGHT',
                description: '全ての味方の最大LIFEの30%を回復するが、使用後3-5ターン行動不能になる',
                healingRate: 0.30,     // 最大LIFEの30%回復
                incapacitationDuration: { min: 3, max: 5 },  // 3-5ターン行動不能
                targetType: 'all_allies'  // 全味方をターゲット
            },
            CRITICAL_SHOT: {
                id: 'critical_shot',
                name: 'CRITICAL SHOT',
                description: '30%の確率で敵を即死させる。失敗時は通常攻撃。ドラゴン系には無効',
                instantKillChance: 0.30,  // 30%の即死確率
                targetType: 'enemy',      // 単体敵をターゲット
                immuneProperties: ['dragon']  // ドラゴン系には無効
            }
        },

        // Level 20 Skills Configuration
        LEVEL_20_SKILLS: {
            DRAGON_STRIKE: {
                id: 'dragon_strike',
                name: 'DRAGON STRIKE',
                description: '自分の最大LIFEの15%を消費し、ドラゴン系の敵に3倍の物理ダメージを与える',
                lifeCostRate: 0.15,    // 最大LIFEの15%を消費
                damageMultiplier: 3.0, // ドラゴン系に3倍ダメージ
                targetType: 'enemy',   // 単体敵をターゲット
                effectiveProperties: ['dragon']  // ドラゴン系に効果的
            },
            PROTECT: {
                id: 'protect',
                name: 'PROTECT',
                description: '全ての味方の物理防御力を2倍にするが、使用後4-6ターン行動不能になる',
                defenseMultiplier: 2.0, // 物理防御力2倍
                duration: { min: 8, max: 12 },  // 8-12ターン持続
                incapacitationDuration: { min: 4, max: 6 },  // 4-6ターン行動不能
                targetType: 'all_allies'  // 全味方をターゲット
            },
            SWIFT_MOVEMENT: {
                id: 'swift_movement',
                name: 'SWIFT MOVEMENT',
                description: '自分の敏捷性を2倍にし、12-16ターンの間効果が持続する',
                agilityMultiplier: 2.0, // 敏捷性2倍
                duration: { min: 12, max: 16 },  // 12-16ターン持続
                targetType: 'self'      // 自分をターゲット
            }
        },
        
        SKILL_SLOTS: {
            WARRIOR: [
                { level: 5, name: 'SHIELD', description: 'すべての敵の攻撃を自分が引き受ける。受けるダメージは1/3になる。2回行動するまで有効', skillId: 'shield' },
                { level: 10, name: 'TRIPLE STRIKE', description: '単体の敵に3回連続攻撃を行う。各攻撃は通常攻撃と同じダメージ計算', skillId: 'triple_strike' },
                { level: 15, name: 'MELEE', description: '自分の最大LIFEの15%を消費し、全ての敵に2倍の物理ダメージを与える範囲攻撃', skillId: 'melee' },
                { level: 20, name: 'DRAGON STRIKE', description: '自分の最大LIFEの15%を消費し、ドラゴン系の敵に3倍の物理ダメージを与える', skillId: 'dragon_strike' },
                { level: 25, name: 'Skill 5', description: 'To be implemented' },
                { level: 30, name: 'Skill 6', description: 'To be implemented' }
            ],
            MAGE: [
                { level: 5, name: 'EXORCISE', description: '霊体の敵に3倍のダメージを与える魔法攻撃。単体対象', skillId: 'exorcise' },
                { level: 10, name: 'SILENCE', description: '全ての敵を沈黙状態にし、5-8ターンの間魔法攻撃を封じる', skillId: 'silence' },
                { level: 15, name: 'HEALING LIGHT', description: '全ての味方の最大LIFEの30%を回復するが、使用後3-5ターン行動不能になる', skillId: 'healing_light' },
                { level: 20, name: 'PROTECT', description: '全ての味方の物理防御力を2倍にするが、使用後4-6ターン行動不能になる', skillId: 'protect' },
                { level: 25, name: 'Skill 5', description: 'To be implemented' },
                { level: 30, name: 'Skill 6', description: 'To be implemented' }
            ],
            RANGER: [
                { level: 5, name: 'FIRST AID', description: 'ターゲットとして選択した味方一人のLIFEを35％回復', skillId: 'first_aid' },
                { level: 10, name: 'HIDE', description: '6ターンの間隠れ状態になり、敵の攻撃対象から除外される。攻撃は可能', skillId: 'hide' },
                { level: 15, name: 'CRITICAL SHOT', description: '30%の確率で敵を即死させる。失敗時は通常攻撃。ドラゴン系には無効', skillId: 'critical_shot' },
                { level: 20, name: 'SWIFT MOVEMENT', description: '自分の敏捷性を2倍にし、12-16ターンの間効果が持続する', skillId: 'swift_movement' },
                { level: 25, name: 'Skill 5', description: 'To be implemented' },
                { level: 30, name: 'Skill 6', description: 'To be implemented' }
            ]
        }
    },

    // Atmospheric Effects Configuration
    ATMOSPHERIC_EFFECTS: {
        ENABLED: true,                    // Always enable atmospheric effects
        PARTICLE_COUNT: 45,               // Enhanced particle count for better visibility
        CONTINUOUS_RENDERING: true,       // Enable continuous rendering for smooth animation
        RENDER_IN_COMBAT: false,         // Disable during combat for performance
        RENDER_DURING_EXPLORATION: true   // Always render during exploration
    },

    // UI Update Events
    EVENTS: {
        GAME_STATE_CHANGED: 'gameStateChanged',
        PLAYER_MOVED: 'playerMoved',
        COMBAT_STARTED: 'combatStarted',
        COMBAT_ENDED: 'combatEnded',
        LEVEL_UP: 'levelUp',
        FLOOR_CHANGED: 'floorChanged',
        MAPPING_TOOL_OBTAINED: 'mappingToolObtained',
        SHOP_OPENED: 'shopOpened'
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONSTANTS;
}

// Make available globally for browser
if (typeof window !== 'undefined') {
    window.CONSTANTS = CONSTANTS;
}
